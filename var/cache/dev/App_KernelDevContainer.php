<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerIcnYPpS\App_KernelDevContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerIcnYPpS/App_KernelDevContainer.php') {
    touch(__DIR__.'/ContainerIcnYPpS.legacy');

    return;
}

if (!\class_exists(App_KernelDevContainer::class, false)) {
    \class_alias(\ContainerIcnYPpS\App_KernelDevContainer::class, App_KernelDevContainer::class, false);
}

return new \ContainerIcnYPpS\App_KernelDevContainer([
    'container.build_hash' => 'IcnYPpS',
    'container.build_id' => '6fd89081',
    'container.build_time' => 1751507028,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerIcnYPpS');
