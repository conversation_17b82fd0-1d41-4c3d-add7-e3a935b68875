<?php

namespace ContainerIcnYPpS;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getFetchPricesCommandService extends App_KernelDevContainer
{
    /*
     * Gets the private 'App\Command\FetchPricesCommand' shared autowired service.
     *
     * @return \App\Command\FetchPricesCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        $a = ($container->privates['logger'] ?? self::getLoggerService($container));

        $container->privates['App\\Command\\FetchPricesCommand'] = $instance = new \App\Command\FetchPricesCommand(new \App\Service\PriceFetcherService(new \App\Service\MockApiService($a), ($container->privates['App\\Repository\\ProductPriceRepository'] ?? $container->load('getProductPriceRepositoryService')), $a, ['api1' => new \App\Mapper\Api1Mapper(), 'api2' => new \App\Mapper\Api2Mapper()]));

        $instance->setName('app:fetch-prices');

        return $instance;
    }
}
