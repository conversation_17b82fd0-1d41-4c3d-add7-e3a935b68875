<?php

namespace ContainerIcnYPpS;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getProductPriceRepositoryService extends App_KernelDevContainer
{
    /*
     * Gets the private 'App\Repository\ProductPriceRepository' shared autowired service.
     *
     * @return \App\Repository\ProductPriceRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['App\\Repository\\ProductPriceRepository'] = new \App\Repository\ProductPriceRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
