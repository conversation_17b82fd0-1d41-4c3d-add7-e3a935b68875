<?php

namespace ContainerAhqdvp5;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getDoctrine_Orm_EntityValueResolverService extends App_KernelProdContainer
{
    /*
     * Gets the private 'doctrine.orm.entity_value_resolver' shared service.
     *
     * @return \Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['doctrine.orm.entity_value_resolver'] = new \Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver(($container->services['doctrine'] ?? self::getDoctrineService($container)), NULL, new \Symfony\Bridge\Doctrine\Attribute\MapEntity(NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, false), []);
    }
}
