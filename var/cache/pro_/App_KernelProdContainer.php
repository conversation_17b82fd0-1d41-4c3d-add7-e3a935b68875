<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerAhqdvp5\App_KernelProdContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerAhqdvp5/App_KernelProdContainer.php') {
    touch(__DIR__.'/ContainerAhqdvp5.legacy');

    return;
}

if (!\class_exists(App_KernelProdContainer::class, false)) {
    \class_alias(\ContainerAhqdvp5\App_KernelProdContainer::class, App_KernelProdContainer::class, false);
}

return new \ContainerAhqdvp5\App_KernelProdContainer([
    'container.build_hash' => 'Ahqdvp5',
    'container.build_id' => '55ab8716',
    'container.build_time' => 1751507327,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerAhqdvp5');
