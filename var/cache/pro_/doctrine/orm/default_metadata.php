<?php

// This file has been auto-generated by the Symfony Cache Component.

return [[

'App__Entity__ProductPrice__CLASSMETADATA__' => 0,

], [

0 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\ClassMetadata')),
            clone ($p['Doctrine\\ORM\\Id\\SequenceGenerator'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Id\\SequenceGenerator')),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'App\\Entity\\ProductPrice',
                ],
                'namespace' => [
                    'App\\Entity',
                ],
                'rootEntityName' => [
                    'App\\Entity\\ProductPrice',
                ],
                'identifier' => [
                    [
                        'id',
                    ],
                ],
                'generatorType' => [
                    2,
                ],
                'fieldMappings' => [
                    [
                        'id' => [
                            'fieldName' => 'id',
                            'type' => 'integer',
                            'scale' => null,
                            'length' => null,
                            'unique' => false,
                            'nullable' => false,
                            'precision' => null,
                            'id' => true,
                            'columnName' => 'id',
                        ],
                        'productId' => [
                            'fieldName' => 'productId',
                            'type' => 'string',
                            'scale' => null,
                            'length' => 255,
                            'unique' => false,
                            'nullable' => false,
                            'precision' => null,
                            'columnName' => 'product_id',
                        ],
                        'vendor' => [
                            'fieldName' => 'vendor',
                            'type' => 'string',
                            'scale' => null,
                            'length' => 255,
                            'unique' => false,
                            'nullable' => false,
                            'precision' => null,
                            'columnName' => 'vendor',
                        ],
                        'price' => [
                            'fieldName' => 'price',
                            'type' => 'float',
                            'scale' => null,
                            'length' => null,
                            'unique' => false,
                            'nullable' => false,
                            'precision' => null,
                            'columnName' => 'price',
                        ],
                        'fetchedAt' => [
                            'fieldName' => 'fetchedAt',
                            'type' => 'datetime_immutable',
                            'scale' => null,
                            'length' => null,
                            'unique' => false,
                            'nullable' => false,
                            'precision' => null,
                            'columnName' => 'fetched_at',
                        ],
                    ],
                ],
                'fieldNames' => [
                    [
                        'id' => 'id',
                        'product_id' => 'productId',
                        'vendor' => 'vendor',
                        'price' => 'price',
                        'fetched_at' => 'fetchedAt',
                    ],
                ],
                'columnNames' => [
                    [
                        'id' => 'id',
                        'productId' => 'product_id',
                        'vendor' => 'vendor',
                        'price' => 'price',
                        'fetchedAt' => 'fetched_at',
                    ],
                ],
                'table' => [
                    [
                        'name' => 'product_prices',
                    ],
                ],
                'idGenerator' => [
                    $o[1],
                ],
                'sequenceGeneratorDefinition' => [
                    [
                        'sequenceName' => 'product_prices_id_seq',
                        'allocationSize' => '1',
                        'initialValue' => '1',
                    ],
                ],
            ],
        ],
        $o[0],
        [
            -1 => [
                'allocationSize' => 1,
                'sequenceName' => 'product_prices_id_seq',
            ],
        ]
    );
},

]];
